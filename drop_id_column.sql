-- 删除 wdt_saledetail 表的 id 主键列
-- 执行前请确保：
-- 1. 已备份数据库
-- 2. 确认没有其他表引用此主键
-- 3. 确认应用程序不依赖此 id 字段
-- 4. 在非生产环境先测试

USE qq_day_sale;

-- 步骤1: 检查表当前状态
SELECT 
    COLUMN_NAME,
    DATA_TYPE,
    IS_NULLABLE,
    COLUMN_KEY,
    EXTRA
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'qq_day_sale' 
  AND TABLE_NAME = 'wdt_saledetail' 
  AND COLUMN_NAME = 'id';

-- 步骤2: 检查是否有外键引用此主键
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE
WHERE REFERENCED_TABLE_SCHEMA = 'qq_day_sale'
  AND REFERENCED_TABLE_NAME = 'wdt_saledetail'
  AND REFERENCED_COLUMN_NAME = 'id';

-- 步骤3: 删除主键约束
-- 注意：MySQL 要求先删除主键约束，再删除列
ALTER TABLE `wdt_saledetail` DROP PRIMARY KEY;

-- 步骤4: 删除 id 列
ALTER TABLE `wdt_saledetail` DROP COLUMN `id`;

-- 步骤5: 验证删除结果
DESCRIBE `wdt_saledetail`;

-- 步骤6: 检查表结构是否正确
SHOW CREATE TABLE `wdt_saledetail`;
